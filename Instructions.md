We need to implement a new MRA API endpoint for AWB Release Orders (AWB Validation) that integrates with the existing MRA system infrastructure.

**API Endpoint Specification:**
- URL: `https://eservices.mra.mw/ws/IATACoreAPI/api/v1/releaseorders`
- Authentication: Use existing MRA authentication (ClientId: 'aircargo', ClientSecret: 'IPass2025!')
- Method: POST
- Content-Type: application/json

**Request Format:**
```json
{
  "AirWayBillNumber": "16707072376"
}
```

**Expected Response Format:**
The API returns a JSON response with success status, message, and detailed release order data including station information, declarant details, importer/exporter information, declaration details, assessment information, receipt data, summary statistics, and an array of items with their specifications.

**Database Implementation Requirements:**

1. **Create new table `mra_release_orders`** with the following columns:
   - id (primary key)
   - airwaybill_number (varchar, indexed)
   - station_code (varchar)
   - station_name (varchar)
   - declarant_code (varchar)
   - declarant_name (text)
   - importer_tin (varchar)
   - importer_name (text)
   - exporter_tin (varchar)
   - exporter_name (text)
   - declaration_serial (varchar)
   - declaration_number (integer)
   - declaration_year (integer)
   - declaration_registration_date (timestamp)
   - assessment_serial (varchar)
   - assessment_number (integer)
   - assessment_date (timestamp)
   - receipt_serial (varchar)
   - receipt_number (integer)
   - receipt_date (timestamp)
   - receipt_amount (decimal)
   - fin_amount_tbp (decimal)
   - number_of_packages (integer)
   - number_of_items (integer)
   - total_weight (decimal, nullable)
   - items (json)
   - created_at (timestamp)
   - updated_at (timestamp)

2. **Create Laravel implementation (DDD Pattern and Helper Classes)** including:
   - Migration file for the new table
   - Eloquent model `MraReleaseOrder`
   - Service class `MraReleaseOrderService` for API integration
   - Controller `MraReleaseOrderController` with methods to fetch and store release orders
   - Routes for the new functionality
   - Proper error handling and logging

3. **Integration Setup:**

   **Key Change:** Use declaration numbers instead of validation numbers
   - Format: `declaration_serial + declaration_number = C 123`
   - Each declaration number must be unique

   **What to do:**
   - Copy how MRA integration works in existing code
   - Check AWB numbers are valid before processing
   - Save API responses to avoid calling the same data twice
   - Add database indexes on frequently searched fields
   - Connect to existing `mra_validations` table
   - Store dates in proper database format (YYYY-MM-DD HH:MM:SS)
   - Handle when API is slow or fails to respond

4. **UI Requirements:**
   - Add interface to search and display release order information
   - Integrate with existing MRA dashboard
   - Show release order status and details in a user-friendly format

Please implement this step by step, starting with the database migration and model, then the service layer, controller, and finally the UI components.