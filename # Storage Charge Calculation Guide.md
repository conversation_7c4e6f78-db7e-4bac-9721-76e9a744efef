# Storage Charge Calculation Guide

This document provides a detailed explanation of how storage charges are calculated in the cargo handling system, including the timing, formulas, and special cases.

## Table of Contents

1. [Storage Charge Calculation Process](#storage-charge-calculation-process)
2. [Storage Days Calculation](#storage-days-calculation)
3. [Free Days Application](#free-days-application)
4. [Storage Charge Formula](#storage-charge-formula)
5. [Calculation Triggers](#calculation-triggers)
6. [Special Cases and Edge Conditions](#special-cases-and-edge-conditions)
7. [Examples](#examples)

## Storage Charge Calculation Process

Storage charges are calculated at several points in the cargo handling workflow:

1. **Initial Calculation**: When a waybill is first processed and notified
2. **Recalculation**: When charges are recalculated due to exchange rate changes
3. **Payment Processing**: When a payment is being processed
4. **Quotation Generation**: When creating a quotation for a client

The calculation process follows these steps:

1. Determine the applicable storage type based on cargo characteristics
2. Calculate the number of storage days
3. Apply free days based on storage type
4. Calculate the storage charge amount
5. Apply exchange rates and taxes

## Storage Days Calculation

The calculation of storage days is a critical component and varies based on storage type:

### Regular Storage Days Calculation

```php
// In ChargeCalculationHelper::calculateStorageDaysShared
public static function calculateStorageDaysShared($waybillId, $storageTypeId, $isHouse = false, $startDate = null, $endDate = null)
{
    // For quotations with explicit dates
    if ($startDate && $endDate) {
        // Calculate days between dates (inclusive)
        $interval = date_diff($endDateObj, $startDateObj);
        $days = $interval->days + 1;
        return $days;
    }
    
    // For regular charge calculations
    $notification = WaybillNotification::where('waybill_id', $waybillId)
        ->where('is_house', $isHouse)
        ->where('notification_type', 'INITIAL')
        ->orderBy('created_at', 'asc')
        ->first();

    // Determine which date to use for calculating storage days
    $useStartChargingDate = $storageType->is_temperature_controlled ? false : true;

    $notificationDate = $notification->created_at->format('Y-m-d');
    
    if ($useStartChargingDate) {
        // For dry storage, add a 3-day grace period
        $dateToUse = date('Y-m-d', strtotime($notificationDate . ' + 3 days'));
    } else {
        // For cold storage, start charging immediately
        $dateToUse = $notificationDate;
    }

    // Calculate days in storage
    $today = $endDate ?? now()->format('Y-m-d');
    $interval = date_diff($currentDate, $storageDate);
    
    // If today is before or equal to the storage date, return 0 days
    if ($today <= $dateToUse) {
        return 0;
    }

    // Calculate days in storage
    $daysInStorage = $interval->format('%a');

    // Return at least 1 day if there are any days in storage
    return max(1, $daysInStorage);
}
```

### Key Points:

1. **Notification Date**: The starting point is the date when the initial notification was sent for the waybill
2. **Cold vs. Dry Storage**: 
   - Cold storage (temperature-controlled): Charges start from the notification date
   - Dry storage: Charges start 3 days after the notification date
3. **Minimum Days**: At least 1 day is charged if the cargo is in storage
4. **Current Date**: For regular calculations, the end date is the current date
5. **Quotations**: For quotations, explicit start and end dates are used

## Free Days Application

Free days are applied after calculating the total storage days:

```php
// Get the storage type to determine free days
$storageType = StorageType::find($storageTypeId);
$freeDays = $storageType ? $storageType->free_days : 0;

// Calculate chargeable days (total days - free days)
$chargeableDays = max(0, $days - $freeDays);
```

Free days are configured at the storage type level and are subtracted from the total days to determine the chargeable days. If the free days exceed the total days, the chargeable days are set to 0.

## Storage Charge Formula

The actual storage charge calculation uses this formula:

```php
// Using max() to implement the formula: daily_storage = max(weight * rate, minimum_storage)
$weightRate = $weight * $rate;
$dailyStorage = max($weightRate, $minCharge);

// Calculate total amount: total_storage = daily_storage * number_of_chargeable_days
$amount = $dailyStorage * $chargeableDays;
$amountLocal = $amount * $exchangeRate;

// Calculate tax if applicable
$isTaxable = $charge['is_taxable'] ?? true;
$tax = $isTaxable ? ($amountLocal * ($taxPercentage / 100)) : 0;
```

### Key Components:

1. **Daily Storage Rate**: The greater of (weight × rate) or the minimum charge
2. **Total Storage Amount**: Daily storage rate × chargeable days
3. **Local Currency Conversion**: Amount in USD × exchange rate
4. **Tax Calculation**: Local amount × tax percentage (if taxable)

## Calculation Triggers

Storage charges are recalculated in the following scenarios:

1. **Manual Recalculation**: When an admin clicks the "Recalculate All" button
2. **Exchange Rate Update**: When exchange rates are updated
3. **Payment Processing**: Before processing a payment
4. **Quotation Generation**: When creating a new quotation

During recalculation, the system:
1. Retrieves the latest exchange rate
2. Recalculates the storage days based on the current date
3. Applies free days based on the storage type
4. Updates the charge amounts

## Special Cases and Edge Conditions

### 1. Cold Storage vs. Dry Storage

**Cold Storage (Temperature Controlled)**:
- Charges start immediately from the notification date
- Often has higher rates due to specialized facilities
- Usually has fewer or no free days

**Dry Storage**:
- Has a 3-day grace period after notification
- Charges start from the 4th day after notification
- May have more free days depending on configuration

### 2. Partial Waybills

Partial waybills are handled differently:

- Only the first partial waybill for an AWB is charged cargo type fees
- All partial waybills are charged storage fees based on their individual weights
- The system checks the `split_sequence` to determine if it's the first partial

```php
// If this is not the first partial waybill, skip cargo type charges
if ($currentPartial && $currentPartial->split_sequence > 1) {
    $skipCargoTypeCharges = true;
}
```

### 3. Special Handling Codes

Special handling codes can modify storage charges:

- Some codes (like "PER" for perishables) may trigger different storage types
- Special handling codes can be linked to specific storage charges
- The system checks for special handling codes when determining applicable charges

```php
if ($waybill->special_handling_code) {
    $codes = explode(' ', $waybill->special_handling_code);
    foreach ($codes as $code) {
        $shc = DB::table('special_handling_codes')->where('code', $code)->first();
        if ($shc) {
            $charges = StorageCharge::where('special_handling_code_id', $shc->id)
                ->where('is_active', true)
                ->get();
            // Apply these charges
        }
    }
}
```

### 4. Exchange Rate and Storage Days Interaction

When recalculating charges:

- Exchange rate updates affect the local currency amount but not the USD amount
- Storage days recalculation affects both USD and local currency amounts
- The system updates both simultaneously during recalculation

This can lead to confusion if users expect only exchange rate changes but see total amount changes due to increased storage days.

### 5. Retroactive Changes

The system handles retroactive changes as follows:

- **Storage Type Changes**: Only affect new calculations, not existing ones
- **Rate Changes**: Only affect new calculations, not existing ones
- **Free Days Changes**: Only affect new calculations, not existing ones

To apply retroactive changes to existing charges, an admin must manually recalculate the charges.

## Examples

### Example 1: Cold Storage Calculation

**Scenario**:
- Notification date: May 15, 2025
- Current date: May 20, 2025
- Weight: 100 kg
- Rate: $0.25 per kg
- Minimum charge: $30
- Free days: 1
- Exchange rate: 2100 MWK per USD

**Calculation**:
1. Total days: May 20 - May 15 = 5 days
2. Free days: 1 day
3. Chargeable days: 5 - 1 = 4 days
4. Daily storage: max(100 × 0.25, 30) = $30
5. Total storage (USD): $30 × 4 = $120
6. Total storage (MWK): $120 × 2100 = 252,000 MWK
7. Tax (16.5%): 252,000 × 0.165 = 41,580 MWK
8. Total with tax: 252,000 + 41,580 = 293,580 MWK

### Example 2: Dry Storage Calculation

**Scenario**:
- Notification date: May 15, 2025
- Current date: May 20, 2025
- Weight: 100 kg
- Rate: $0.20 per kg
- Minimum charge: $25
- Free days: 2
- Exchange rate: 2100 MWK per USD

**Calculation**:
1. Start charging date: May 15 + 3 = May 18
2. Total days: May 20 - May 18 = 2 days
3. Free days: 2 days
4. Chargeable days: 2 - 2 = 0 days
5. Total storage: $0 (no chargeable days)

This example shows how dry storage with the 3-day grace period combined with free days can result in no storage charges for short-term storage.