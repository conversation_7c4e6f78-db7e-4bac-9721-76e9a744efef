## Storage Calculation
1. Calculate Storage Days:
   - For regular storage: days = current_date - notification_date (or notification_date + 3 days for dry storage)
   - Minimum of 1 day

2. Apply Free Days:
   - free_days = storage_type.free_days (from the storage type configuration)
   - chargeable_days = max(0, days - free_days)

3. Calculate Daily Storage Rate:
   - daily_storage = max(weight * rate, min_charge)
   
4. Calculate Amount in USD:
   - amount_usd = daily_storage * chargeable_days

5. Convert to Local Currency:
   - amount_local = amount_usd * exchange_rate

6. Calculate Tax (if applicable):
   - tax_amount = amount_local * tax_percentage (typically 16.5%)

7. Calculate Total Amount:
   - total_amount_local = amount_local + tax_amount

   The key part of this formula is that we're using chargeable_days (which is days - free_days) instead of the total days when calculating the amount.
------------------

## Charges Calculations
1. Calculate Amount in USD:
   - amount_usd = max(weight * rate, min_charge)

2. Convert to Local Currency:
   - amount_local = amount_usd * exchange_rate

3. Calculate Tax (if applicable):
   - tax_amount = amount_local * tax_percentage (typically 16.5%)

4. Calculate Total Amount:
   - total_amount_local = amount_local + tax_amount
---------------------

## Forklift charges
1. Calculate Amount in USD:
   - amount_usd = rate_per_hour * hours

2. Convert to Local Currency:
   - amount_local = amount_usd * exchange_rate

3. Calculate Tax (if applicable):
   - tax_amount = amount_local * tax_percentage (typically 16.5%)

4. Calculate Total Amount:
   - total_amount_local = amount_local + tax_amount

## 4. Total Charges Formula
1. Calculate Subtotal in USD:
   - subtotal_usd = sum of all cargo, storage, and forklift amounts in USD

2. Calculate Subtotal in Local Currency:
   - subtotal_local = sum of all cargo, storage, and forklift amounts in local currency

3. Calculate Total Tax:
   - total_tax = sum of all tax amounts

4. Calculate Total in USD:
   - total_usd = subtotal_usd

5. Calculate Total in Local Currency:
   - total_local = subtotal_local + total_tax